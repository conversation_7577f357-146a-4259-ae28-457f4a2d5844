2025-08-08 18:38:50 - model_training - INFO - 模型名称: Random Forest
2025-08-08 18:38:50 - model_training - INFO - 准确率: 0.7000
2025-08-08 18:38:50 - model_training - INFO - AUC: 0.8313
2025-08-08 18:38:50 - model_training - INFO - AUPRC: 0.8569
2025-08-08 18:38:50 - model_training - INFO - 混淆矩阵:
2025-08-08 18:38:50 - model_training - INFO - 
[[13  7]
 [ 5 15]]
2025-08-08 18:38:50 - model_training - INFO - 
分类报告:
2025-08-08 18:38:50 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.72      0.65      0.68        20
           1       0.68      0.75      0.71        20

    accuracy                           0.70        40
   macro avg       0.70      0.70      0.70        40
weighted avg       0.70      0.70      0.70        40

2025-08-08 18:38:50 - model_training - INFO - 训练时间: 0.25 秒
2025-08-08 18:38:50 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.7000
2025-08-08 18:38:50 - model_training - INFO - 模型 RandomForest 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-08 18:38:50 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-08-08 18:45:41 - model_training - INFO - 模型名称: Random Forest
2025-08-08 18:45:41 - model_training - INFO - 准确率: 0.8750
2025-08-08 18:45:41 - model_training - INFO - AUC: 0.9412
2025-08-08 18:45:41 - model_training - INFO - AUPRC: 0.9359
2025-08-08 18:45:41 - model_training - INFO - 混淆矩阵:
2025-08-08 18:45:41 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-08 18:45:41 - model_training - INFO - 
分类报告:
2025-08-08 18:45:41 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-08 18:45:41 - model_training - INFO - 训练时间: 0.09 秒
2025-08-08 18:45:41 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8750
2025-08-08 18:45:41 - model_training - INFO - 模型 RandomForest 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_184541\models\RandomForest_single_184541.joblib
2025-08-08 18:45:41 - model_training - INFO - 模型 RandomForest 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-08 18:45:41 - model_training - INFO - 模型名称: SVM
2025-08-08 18:45:41 - model_training - INFO - 准确率: 0.7750
2025-08-08 18:45:41 - model_training - INFO - AUC: 0.9182
2025-08-08 18:45:41 - model_training - INFO - AUPRC: 0.9034
2025-08-08 18:45:41 - model_training - INFO - 混淆矩阵:
2025-08-08 18:45:41 - model_training - INFO - 
[[19  4]
 [ 5 12]]
2025-08-08 18:45:41 - model_training - INFO - 
分类报告:
2025-08-08 18:45:41 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.79      0.83      0.81        23
           1       0.75      0.71      0.73        17

    accuracy                           0.78        40
   macro avg       0.77      0.77      0.77        40
weighted avg       0.77      0.78      0.77        40

2025-08-08 18:45:41 - model_training - INFO - 训练时间: 0.01 秒
2025-08-08 18:45:41 - model_training - INFO - 模型 SVM 性能: 准确率=0.7750
2025-08-08 18:45:41 - model_training - INFO - 模型 SVM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_184541\models\SVM_single_184541.joblib
2025-08-08 18:45:41 - model_training - INFO - 模型 SVM 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-08-08 19:01:34 - model_training - INFO - 模型名称: Random Forest
2025-08-08 19:01:34 - model_training - INFO - 准确率: 0.8750
2025-08-08 19:01:34 - model_training - INFO - AUC: 0.9412
2025-08-08 19:01:34 - model_training - INFO - AUPRC: 0.9359
2025-08-08 19:01:34 - model_training - INFO - 混淆矩阵:
2025-08-08 19:01:34 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-08 19:01:34 - model_training - INFO - 
分类报告:
2025-08-08 19:01:34 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-08 19:01:34 - model_training - INFO - 训练时间: 0.09 秒
2025-08-08 19:01:34 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8750
2025-08-08 19:01:34 - model_training - INFO - 模型 RandomForest 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_190134\models\RandomForest_single_190134.joblib
2025-08-08 19:01:34 - model_training - INFO - 模型 RandomForest 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-08 20:16:53 - model_training - INFO - 模型名称: Logistic Regression
2025-08-08 20:16:53 - model_training - INFO - 准确率: 0.5500
2025-08-08 20:16:53 - model_training - INFO - AUC: 0.6125
2025-08-08 20:16:53 - model_training - INFO - AUPRC: 0.4726
2025-08-08 20:16:53 - model_training - INFO - 混淆矩阵:
2025-08-08 20:16:53 - model_training - INFO - 
[[12 15]
 [ 3 10]]
2025-08-08 20:16:53 - model_training - INFO - 
分类报告:
2025-08-08 20:16:53 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.80      0.44      0.57        27
           1       0.40      0.77      0.53        13

    accuracy                           0.55        40
   macro avg       0.60      0.61      0.55        40
weighted avg       0.67      0.55      0.56        40

2025-08-08 20:16:53 - model_training - INFO - 训练时间: 0.01 秒
2025-08-08 20:16:53 - model_training - INFO - 模型 Logistic 性能: 准确率=0.5500
2025-08-08 20:16:53 - model_training - INFO - 模型 Logistic 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-08-08 20:16:53 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_feature_names.joblib
2025-08-08 20:17:21 - model_training - INFO - 模型名称: Logistic Regression
2025-08-08 20:17:21 - model_training - INFO - 准确率: 0.5500
2025-08-08 20:17:21 - model_training - INFO - AUC: 0.6125
2025-08-08 20:17:21 - model_training - INFO - AUPRC: 0.4726
2025-08-08 20:17:21 - model_training - INFO - 混淆矩阵:
2025-08-08 20:17:21 - model_training - INFO - 
[[12 15]
 [ 3 10]]
2025-08-08 20:17:21 - model_training - INFO - 
分类报告:
2025-08-08 20:17:21 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.80      0.44      0.57        27
           1       0.40      0.77      0.53        13

    accuracy                           0.55        40
   macro avg       0.60      0.61      0.55        40
weighted avg       0.67      0.55      0.56        40

2025-08-08 20:17:21 - model_training - INFO - 训练时间: 0.01 秒
2025-08-08 20:17:21 - model_training - INFO - 模型 Logistic 性能: 准确率=0.5500
2025-08-08 20:17:21 - model_training - INFO - 模型 Logistic 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-08-08 20:17:21 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_feature_names.joblib
2025-08-08 21:19:24 - model_training - INFO - 模型名称: Random Forest
2025-08-08 21:19:24 - model_training - INFO - 准确率: 0.8780
2025-08-08 21:19:24 - model_training - INFO - AUC: 0.9481
2025-08-08 21:19:24 - model_training - INFO - AUPRC: 0.9453
2025-08-08 21:19:24 - model_training - INFO - 混淆矩阵:
2025-08-08 21:19:24 - model_training - INFO - 
[[21  2]
 [ 3 15]]
2025-08-08 21:19:24 - model_training - INFO - 
分类报告:
2025-08-08 21:19:24 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.83      0.86        18

    accuracy                           0.88        41
   macro avg       0.88      0.87      0.88        41
weighted avg       0.88      0.88      0.88        41

2025-08-08 21:19:24 - model_training - INFO - 训练时间: 0.08 秒
2025-08-08 21:19:24 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8780
2025-08-08 21:19:24 - model_training - INFO - 模型 RandomForest 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_211924\models\RandomForest_single_211924.joblib
2025-08-08 21:19:24 - model_training - INFO - 模型 RandomForest 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-08 21:27:50 - model_training - INFO - 模型名称: Random Forest
2025-08-08 21:27:50 - model_training - INFO - 准确率: 0.7000
2025-08-08 21:27:50 - model_training - INFO - AUC: 0.8313
2025-08-08 21:27:50 - model_training - INFO - AUPRC: 0.8569
2025-08-08 21:27:50 - model_training - INFO - 混淆矩阵:
2025-08-08 21:27:50 - model_training - INFO - 
[[13  7]
 [ 5 15]]
2025-08-08 21:27:50 - model_training - INFO - 
分类报告:
2025-08-08 21:27:50 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.72      0.65      0.68        20
           1       0.68      0.75      0.71        20

    accuracy                           0.70        40
   macro avg       0.70      0.70      0.70        40
weighted avg       0.70      0.70      0.70        40

2025-08-08 21:27:50 - model_training - INFO - 训练时间: 0.25 秒
2025-08-08 21:27:50 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.7000
2025-08-08 21:27:50 - model_training - INFO - 模型 RandomForest 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-08 21:27:50 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
